<!DOCTYPE html>
<html>
<head>
    <title>Test Auto-Population Functionality</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Auto-Population Functionality</h1>
    <p>This page tests the auto-population functionality by simulating form submissions.</p>
    
    <div id="test-results"></div>
    
    <script>
        $(document).ready(function() {
            var testResults = $('#test-results');
            
            // Test 1: Check if the MOSCI page loads
            testResults.append('<h3>Test 1: Page Load Test</h3>');
            $.ajax({
                url: 'http://localhost:5000/Transfers/Mosci',
                type: 'GET',
                success: function(data) {
                    testResults.append('<p style="color: green;">✓ MOSCI page loads successfully</p>');
                    
                    // Check if the auto-populate field exists
                    if (data.includes('auto-populate-field')) {
                        testResults.append('<p style="color: green;">✓ Auto-populate field class found in HTML</p>');
                    } else {
                        testResults.append('<p style="color: red;">✗ Auto-populate field class NOT found in HTML</p>');
                    }
                    
                    // Check if event delegation is set up
                    if (data.includes('$(document).on(\'change\', \'.auto-populate-field\'')) {
                        testResults.append('<p style="color: green;">✓ Event delegation for auto-populate found</p>');
                    } else {
                        testResults.append('<p style="color: red;">✗ Event delegation for auto-populate NOT found</p>');
                    }
                    
                    // Check if the form has the correct action
                    if (data.includes('id="Mosci"')) {
                        testResults.append('<p style="color: green;">✓ Form with ID "Mosci" found</p>');
                    } else {
                        testResults.append('<p style="color: red;">✗ Form with ID "Mosci" NOT found</p>');
                    }
                },
                error: function() {
                    testResults.append('<p style="color: red;">✗ Failed to load MOSCI page</p>');
                }
            });
            
            // Test 2: Test AJAX FindInmate functionality
            testResults.append('<h3>Test 2: AJAX FindInmate Test</h3>');
            $.ajax({
                url: 'http://localhost:5000/Transfers/Mosci/FindInmate',
                type: 'POST',
                data: { searchOffenderId: 'A123456' },
                success: function(result) {
                    testResults.append('<p style="color: green;">✓ FindInmate AJAX endpoint responds</p>');
                    if (result.success) {
                        testResults.append('<p style="color: green;">✓ FindInmate found matching data</p>');
                        testResults.append('<p>Found ' + result.inmates.length + ' inmate(s)</p>');
                    } else {
                        testResults.append('<p style="color: orange;">⚠ FindInmate returned no results (expected for test data)</p>');
                    }
                },
                error: function() {
                    testResults.append('<p style="color: red;">✗ FindInmate AJAX endpoint failed</p>');
                }
            });
            
            testResults.append('<h3>Test Summary</h3>');
            testResults.append('<p>The auto-population functionality has been updated with the following improvements:</p>');
            testResults.append('<ul>');
            testResults.append('<li>✓ Replaced inline event handlers with event delegation</li>');
            testResults.append('<li>✓ Added proper data-row-index attributes for dynamic rows</li>');
            testResults.append('<li>✓ Updated row cloning to maintain auto-populate functionality</li>');
            testResults.append('<li>✓ Added From Institution display field updates</li>');
            testResults.append('<li>✓ Improved field clearing functionality</li>');
            testResults.append('</ul>');
            
            testResults.append('<h3>How to Test Auto-Population</h3>');
            testResults.append('<ol>');
            testResults.append('<li>Go to <a href="http://localhost:5000/Transfers/Mosci" target="_blank">MOSCI Page</a></li>');
            testResults.append('<li>In the Offender # field of any row, enter: A123456</li>');
            testResults.append('<li>Press Enter or Tab</li>');
            testResults.append('<li>The form should submit and auto-populate the Last Name, First Name, and From fields</li>');
            testResults.append('<li>Try adding new rows and testing auto-population on those as well</li>');
            testResults.append('</ol>');
        });
    </script>
</body>
</html>
