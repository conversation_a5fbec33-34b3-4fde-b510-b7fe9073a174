@model Odrdc.Dots.Areas.Transfers.Models.Mosci.MosciPageViewModel

@{
    ViewBag.Title = "MOSCI";
}


@if (!string.IsNullOrWhiteSpace(ViewBag.Message))
{
    <div class="alert alert-success fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @ViewBag.Message
    </div>
}
@if (!string.IsNullOrWhiteSpace(ViewBag.ErrorMessage))
{
    <div class="alert alert-danger fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @ViewBag.ErrorMessage
    </div>
}

@if (!string.IsNullOrWhiteSpace(Model.Message))
{
    <div class="alert alert-success fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @Model.Message
    </div>
}
@if (!string.IsNullOrWhiteSpace(Model.ErrorMessage))
{
    <div class="alert alert-danger fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @Model.ErrorMessage
    </div>
}





<div id="divErrorMessage" class="alert alert-danger fade in" style="display:none">
    <a href="#" class="close" data-dismiss="alert" aria-label="close">X</a>
    <span id="ErrorMessage"></span>
</div>

@* Hidden fields for search functionality *@
@Html.HiddenFor(m => m.SearchPrefix)
@Html.HiddenFor(m => m.SearchOffenderId)

@*<div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-12">
                <div class="divFindOffender">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Find Inmate
                        </div>
                        <div class="panel-body">
                            <div class="form-inline">
                                <div class="form-group col-xs-12 col-sm-6 col-md-6">
                                    @Html.DropDownListFor(m => m.InmateIdPrefix, Model.Prefix, new { @class = "form-control" })
                                    @Html.TextBoxFor(m => m.OffenderId, new { @class = "form-control onlyNumeric", @autofocus = "autofocus", @id = "txtInmateNum", maxlength = "6", Value = string.Empty })
                                    <button id="btnFindOffender" type="button" class="btn btn-primary" name="submitAction" value="Search">
                                        <span>Find Inmate</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@

@*<div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-4">
                <select name="InmateIdPrefix" class="form-control input-sm" style="display:inline;width:80px;">
                    @foreach (var item in Model.Prefix)
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                </select>
                <input type="text" name="OffenderId" class="form-control input-sm" placeholder="Offender #" style="display:inline;width:120px;" />
                <button type="button" class="btn btn-success" id="btnFindOffender">
                    <span class="glyphicon glyphicon-search"></span> Find Inmate
                </button>
            </div>
        </div>
    </div>
    <br />*@
@using (Html.BeginForm("Index", "Mosci", new { area = "Transfers" }, FormMethod.Post, true, new { @id = "Mosci", @class = "form-horizontal" }))
{
    @Html.AntiForgeryToken()

    @* Hidden field for JSON model data (used by JavaScript) *@
    @Html.Hidden("modelJson", "", new { id = "modelJson" })

    @* Hidden fields for auto-population functionality *@
    @Html.Hidden("submitAction", "", new { id = "submitAction" })
    @Html.Hidden("autoPopulateRowIndex", "", new { id = "autoPopulateRowIndex" })

    <div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-12">
                <div class="divFindOffender">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Find Inmate
                        </div>
                        <div class="panel-body">
                            <div class="form-inline">
                                <div class="form-group col-xs-12 col-sm-6 col-md-6">
                                    @Html.DropDownListFor(m => m.SearchPrefix, Model.PrefixOptions, new { @class = "form-control input-sm", @id = "searchPrefixDropdown" })
                                    @Html.TextBoxFor(m => m.SearchOffenderId, new { @class = "form-control input-sm onlyNumeric", @autofocus = "autofocus", @id = "txtInmateNum", maxlength = "6" })
                                    <button type="submit" class="btn btn-primary" name="submitAction" value="Search" id="btnFindInmateServer">
                                        <span>Find Inmate</span>
                                    </button>
                                    <button id="btnFindOffender" type="button" class="btn btn-info" title="AJAX search">
                                        <span class="glyphicon glyphicon-search"></span> AJAX Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    //-------------------------------------------------
    <div class="panel panel-primary">
        <div class="panel-heading">
            Schedule Inmate Move&nbsp;&nbsp;-&nbsp;&nbsp;MOSCI
        </div>
        <div class="panel-body">
            <div class="table-responsive">
                <table id="inmateTable" class="table table-bordered table-condensed">
                    <thead class="odrc-header-row">
                        <tr>
                            <td style="width:60px;">Prefix</td>
                            <td style="width:120px;">Offender #</td>
                            <td style="width:150px;">Last Name</td>
                            <td style="width:150px;">First Name</td>
                            <td style="width:120px;">From</td>
                            <td style="width:140px;">To</td>
                            <td style="width:140px;">Scheduled Date</td>
                            <td style="width:80px;">Comments</td>
                            <td style="width:80px;">Remove</td>
                            <td style="width:80px;">Delete</td>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < Model.Inmates.Count; i++)
                        {
                            <tr @if (i == 0) { <text>id="inmate-row-template"</text> }>
                                <td>
                                    @Html.DropDownListFor(m => m.Inmates[i].InmateIdPrefix, Model.PrefixOptions, new { @class = "form-control input-sm" })
                                    @Html.HiddenFor(m => m.Inmates[i].Recno)
                                    @Html.HiddenFor(m => m.Inmates[i].OffenderId)
                                </td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].CombinedOffenderId, new { @class = "form-control input-sm", @maxlength = "7", @onchange = "autoPopulateOffender(this, " + i + ");", @onkeydown = "if(event.keyCode==13||event.keyCode==9){autoPopulateOffender(this, " + i + "); return false;}" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].LastName, new { @class = "form-control input-sm", @readonly = "readonly"})</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].FirstName, new { @class = "form-control input-sm", @readonly = "readonly" })</td>
                                <td>
                                    @{
                                        var fromInstitutionText = "";
                                        if (Model.Inmates[i].FromInstitutionId.HasValue)
                                        {
                                            var fromOption = Model.FromInstitutionOptions.FirstOrDefault(x => x.Value == Model.Inmates[i].FromInstitutionId.ToString());
                                            fromInstitutionText = fromOption?.Text ?? "";
                                        }
                                    }
                                    @Html.TextBoxFor(m => m.Inmates[i].FromInstitutionId, new { @class = "form-control input-sm", @readonly = "readonly", @style = "display:none;" })
                                    @Html.TextBox($"Inmates[{i}].FromInstitutionDisplay", fromInstitutionText, new { @class = "form-control input-sm", @readonly = "readonly" })
                                </td>
                                <td>@Html.DropDownListFor(m => m.Inmates[i].ToInstitutionId, Model.ToInstitutionOptions, new { @class = "form-control input-sm" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].SchDate, "{0:MM/dd/yyyy}", new { @class = "form-control input-sm" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].Descrl, new { @class = "form-control input-sm" })</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForRemoval)</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForDeletion)</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Add / Remove Row Buttons -->
            <div class="row">
                <div class="col-md-6 col-xs-12">
                    <button type="submit" name="submitAction" value="AddNew" id="btnAddNewInmate" class="btn btn-primary">
                        <span class="glyphicon glyphicon-plus"></span> Add New Inmate
                    </button>
                </div>
                <div class="col-md-6 col-xs-12 text-right">
                    <button type="submit" name="submitAction" value="RemoveSelected" name="btnRemoveInmate" class="btn btn-danger">
                        <span class="glyphicon glyphicon-remove"></span> Remove Inmate
                    </button>
                    <span style="display:inline-block; width:20px;"></span>
                    <button type="submit" name="submitAction" value="DeleteSelected" name="btnDelete" class="btn btn-default">
                        <span class="glyphicon glyphicon-trash"></span> Delete
                    </button>
                </div>
            </div>
            <br />
            <!-- Save / Cancel Buttons -->
            <div class="row text-center">
                <button type="submit" name="submitAction" value="Save" class="btn btn-primary" id="btnSave">
                    <span class="glyphicon glyphicon-floppy-disk"></span> Save
                </button>
                <button type="submit" name="submitAction" value="Cancel" class="btn btn-default" id="btnCancel">
                    <span class="glyphicon glyphicon-remove-circle"></span> Cancel
                </button>
            </div>
        </div>
    </div>



    @section Scripts {
        <!-- Use jQuery UI from CDN -->
        <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"
                integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0="
                crossorigin="anonymous"></script>

        <!-- Define MyScript object to prevent errors -->
        <script type="text/javascript">
            // Define MyScript object if it doesn't exist
            var MyScript = MyScript || {};

            // Add init method if it doesn't exist
            MyScript.init = MyScript.init || function(options) {
                console.log("MyScript.init called with options:", options);
                window.m_options = options; // Store options globally if needed
            };

            var MOSCI = MOSCI || {};
            MOSCI.MAX_ROWS = 19;

            // Auto-populate function for offender data
            function autoPopulateOffender(inputElement, rowIndex) {
                var offenderId = inputElement.value.trim();
                if (!offenderId) {
                    // Clear fields if offender ID is empty
                    clearOffenderFields(rowIndex);
                    return;
                }

                // Set hidden fields and submit form for server-side processing
                document.getElementById('submitAction').value = 'AutoPopulate';
                document.getElementById('autoPopulateRowIndex').value = rowIndex;
                document.getElementById('Mosci').submit();
            }

            // Function to clear offender fields
            function clearOffenderFields(rowIndex) {
                var $row = $('#inmateTable tbody tr').eq(rowIndex);
                $row.find('input[id*="LastName"]').val('');
                $row.find('input[id*="FirstName"]').val('');
                $row.find('input[name*="FromInstitutionDisplay"]').val('');
                $row.find('input[id*="FromInstitutionId"]').val('');
            }

            // Function to add a new inmate row
            MOSCI.addNewInmate = function() {
                var currentRowCount = $('#inmateTable tbody tr').length;
                if (currentRowCount >= MOSCI.MAX_ROWS) {
                    console.log('Maximum number of rows reached');
                    return null;
                }

                // Clone the template row
                var $newRow = $('#inmate-row-template').clone();

                // Generate a unique ID for the new row
                var rowId = 'inmate-row-' + new Date().getTime();
                $newRow.attr('id', rowId);

                // Update the name attributes for proper MVC model binding
                var newIndex = currentRowCount;
                $newRow.find('select, input').each(function() {
                    var $element = $(this);
                    var name = $element.attr('name');
                    var id = $element.attr('id');

                    if (name && name.includes('[0]')) {
                        $element.attr('name', name.replace('[0]', '[' + newIndex + ']'));
                    }
                    if (id && id.includes('_0_')) {
                        $element.attr('id', id.replace('_0_', '_' + newIndex + '_'));
                    }
                });

                // Clear all input values
                $newRow.find('input[type="text"]').val('');
                $newRow.find('select').prop('selectedIndex', 0);
                $newRow.find('input[type="checkbox"]').prop('checked', false);
                $newRow.find('input[type="hidden"]').val('');

                // Append the new row to the table
                $('#inmateTable tbody').append($newRow);

                // Apply numeric validation to the new row (exclude CombinedOffenderId fields)
                $newRow.find('.onlyNumeric').not('[id*="CombinedOffenderId"]').on('keypress', function(e) {
                    // Allow only numbers (0-9)
                    if (e.which < 48 || e.which > 57) {
                        e.preventDefault();
                    }
                });

                return $newRow;
            };

            // Function to clear the table except for one empty row
            MOSCI.clearTable = function() {
                $('#inmateTable tbody tr').not(':first').remove();
                var $firstRow = $('#inmateTable tbody tr:first');
                $firstRow.find('input[type="text"]').val('');
                $firstRow.find('select').prop('selectedIndex', 0);
                $firstRow.find('input[type="checkbox"]').prop('checked', false);
                $firstRow.find('input[type="hidden"]').val('');
            };

        $(function () {
            // Debug: Log dropdown options on page load
            console.log('ToInstitutionOptions available:', @Html.Raw(Json.Serialize(Model.ToInstitutionOptions)));
            console.log('FromInstitutionOptions available:', @Html.Raw(Json.Serialize(Model.FromInstitutionOptions)));

            // Debug form submission
            $('#Mosci').on('submit', function(e) {
                console.log('Form submission detected');
                var formData = $(this).serialize();
                console.log('Form data:', formData);

                // Check if this is a search submission
                var submitAction = $('input[name="submitAction"]').val() || $('button[type="submit"]:focus').val();
                console.log('Submit action:', submitAction);

                if (submitAction === 'Search') {
                    console.log('Search form submission detected');
                }
            });

            // Test function to verify dropdown functionality
            window.testDropdownSelection = function(institutionId) {
                console.log('Testing dropdown selection with institutionId:', institutionId);
                var $firstToDropdown = $('#inmateTable tbody tr:first select[id*="ToInstitutionId"]');
                if ($firstToDropdown.length > 0) {
                    console.log('Found To dropdown, setting value to:', institutionId);
                    $firstToDropdown.val(institutionId);
                    console.log('Dropdown value after setting:', $firstToDropdown.val());
                    console.log('Selected option text:', $firstToDropdown.find('option:selected').text());
                } else {
                    console.log('No To dropdown found in first row');
                }
            };

            // Clone first row and clear inputs when adding a new inmate
            $('#btnAddNewInmate').on('click', function (e) {
                e.preventDefault();

                // Check if we've reached the maximum number of rows
                var currentRowCount = $('#inmateTable tbody tr').length;
                if (currentRowCount >= MOSCI.MAX_ROWS) {
                    alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more rows.');
                    return null;
                }

                // Clone the template row
                var $newRow = $('#inmate-row-template').clone();

                // Generate a unique ID for the new row
                var rowId = 'inmate-row-' + new Date().getTime();
                $newRow.attr('id', rowId);

                // Update the name attributes for proper MVC model binding
                var newIndex = currentRowCount;
                $newRow.find('select, input').each(function() {
                    var $element = $(this);
                    var name = $element.attr('name');
                    var id = $element.attr('id');

                    if (name && name.includes('[0]')) {
                        $element.attr('name', name.replace('[0]', '[' + newIndex + ']'));
                    }
                    if (id && id.includes('_0_')) {
                        $element.attr('id', id.replace('_0_', '_' + newIndex + '_'));
                    }
                });

                // Clear all input values
                $newRow.find('input[type="text"]').val('');
                $newRow.find('select').prop('selectedIndex', 0);
                $newRow.find('input[type="checkbox"]').prop('checked', false);
                $newRow.find('input[type="hidden"]').val('');

                // Append the new row to the table
                $('#inmateTable tbody').append($newRow);

                // Apply numeric validation to the new row (exclude CombinedOffenderId fields)
                $newRow.find('.onlyNumeric').not('[id*="CombinedOffenderId"]').on('keypress', function(e) {
                    // Allow only numbers (0-9)
                    if (e.which < 48 || e.which > 57) {
                        e.preventDefault();
                    }
                });

                // Scroll to the new row
                $('html, body').animate({
                    scrollTop: $newRow.offset().top - 100
                }, 500);
            });

            // Handle remove inmate button - using submitAction value instead of name
            $('button[value="RemoveSelected"]').on('click', function(e) {
                e.preventDefault();

                var hasChecked = false;
                var checkedCount = 0;
                var totalRows = $('#inmateTable tbody tr').length;

                // Count checked rows first
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForRemoval"]').is(':checked')) {
                        checkedCount++;
                    }
                });

                if (checkedCount === 0) {
                    alert('Please select at least one inmate to remove.');
                    return;
                }

                // Check if removing would leave at least one row
                if (totalRows - checkedCount < 1) {
                    alert('Cannot remove all rows. At least one row must remain in the table.');
                    return;
                }

                // Remove the checked rows
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForRemoval"]').is(':checked')) {
                        $row.remove();
                        hasChecked = true;
                    }
                });

                if (hasChecked) {
                    alert('Selected inmate(s) removed successfully.');
                }

                updateButtonState();
            });

            // Handle delete button - using submitAction value instead of name
            $('button[value="DeleteSelected"]').on('click', function(e) {
                e.preventDefault();

                var hasChecked = false;
                var checkedCount = 0;
                var totalRows = $('#inmateTable tbody tr').length;

                // Count checked rows first
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForDeletion"]').is(':checked')) {
                        checkedCount++;
                    }
                });

                if (checkedCount === 0) {
                    alert('Please select at least one inmate to delete.');
                    return;
                }

                // Check if deleting would leave at least one row
                if (totalRows - checkedCount < 1) {
                    alert('Cannot delete all rows. At least one row must remain in the table.');
                    return;
                }

                // Delete the checked rows
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForDeletion"]').is(':checked')) {
                        $row.remove();
                        hasChecked = true;
                    }
                });

                if (hasChecked) {
                    alert('Selected inmate(s) deleted successfully.');
                }

                updateButtonState();
            });

            // Handle cancel button
            $('#btnCancel').on('click', function() {
                if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                    window.location.href = '@Url.Action("Index", "Home", new { area = "" })';
                }
            });


            var updateButtonState = function () {
                var rowCount = $('#inmateTable tbody tr').length;
                $('#btnAddNewInmate').prop('disabled', rowCount >= MOSCI.MAX_ROWS);
                console.log('Row count: ' + rowCount + ', Add button ' + (rowCount >= MOSCI.MAX_ROWS ? 'disabled' : 'enabled'));
            };


            updateButtonState();

            // Initialize numeric validation for existing rows (exclude CombinedOffenderId fields)
            $('.onlyNumeric').not('[id*="CombinedOffenderId"]').on('keypress', function(e) {
                // Allow only numbers (0-9), backspace, delete, tab, escape, enter
                if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 110]) !== -1 ||
                    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                    (e.keyCode === 65 && e.ctrlKey === true) ||
                    (e.keyCode === 67 && e.ctrlKey === true) ||
                    (e.keyCode === 86 && e.ctrlKey === true) ||
                    (e.keyCode === 88 && e.ctrlKey === true) ||
                    // Allow home, end, left, right
                    (e.keyCode >= 35 && e.keyCode <= 39)) {
                    return;
                }
                // Ensure that it is a number and stop the keypress
                if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                    e.preventDefault();
                }
            });

            // Add event handler for prefix dropdown changes to update combined offender ID
            $(document).on('change', 'select[id*="InmateIdPrefix"]', function() {
                var $row = $(this).closest('tr');
                var prefix = $(this).val();
                var offenderId = $row.find('input[id*="OffenderId"]').val();
                var combinedId = prefix && offenderId ? prefix + offenderId : '';
                $row.find('input[id*="CombinedOffenderId"]').val(combinedId);
            });

            MyScript.init({
                MOSCI: '@Url.Action("Mosci", "Mosci", new { area = "Transfers" })',
                    GetEmployeeInfoByOaksId: '@Url.Action("GetEmployeeInfoByOaksId", "Mosci", new { area = "Transfers" })'
                });


            // Handle server-side Find Inmate button
            $('#btnFindInmateServer').on('click', function(e) {
                console.log('Find Inmate (Server) button clicked');

                var prefix = $('#searchPrefixDropdown').val();
                var offenderId = $('#txtInmateNum').val();

                console.log('Form values - Prefix:', prefix, 'OffenderId:', offenderId);

                // Validate offenderId
                if (!offenderId || offenderId.trim() === '') {
                    alert('Please enter an Offender ID.');
                    e.preventDefault();
                    return false;
                }

                console.log('Validation passed, submitting form...');
                // Let the form submit naturally
                return true;
            });

            // Handle AJAX find offender button
            $('#btnFindOffender').click(function (e) {
                e.preventDefault();
                var prefix = $('#searchPrefixDropdown').val();
                var offenderId = $('#txtInmateNum').val();

                // Validate offenderId
                if (!offenderId || offenderId.trim() === '') {
                    alert('Please enter an Offender ID.');
                    return;
                }

                // Trim any whitespace
                offenderId = offenderId.trim();

                // Concatenate the prefix with the offender ID for searching
                var searchOffenderId = prefix + offenderId;

                // Log the values for debugging
                console.log('Searching for inmate with prefix:', prefix, 'and offenderId:', offenderId);
                console.log('Combined search ID:', searchOffenderId);

                $(this).prop('disabled', true)
                       .html('<span class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></span> Searching…');

                MOSCI.clearTable();

                $.ajax({
                    url: '@Url.Action("FindInmate", "Mosci", new { area = "Transfers" })',
                    type: 'POST',
                    data: { searchOffenderId: searchOffenderId },
                    success: function (result) {
                        $('#btnFindOffender').prop('disabled', false).html('<span>Find Inmate</span>');
                        console.log('Server response:', result);

                        if (result.success) {
                            var inmates = result.inmates;
                            var $first = $('#inmateTable tbody tr').first();
                            var isEmpty = !$first.find('input[id*="CombinedOffenderId"]').val();

                            inmates.forEach(function (inmate, i) {
                                var $row;

                                if (i === 0 && isEmpty) {
                                    $row = $first;
                                } else {
                                    // Use the MOSCI.addNewInmate function to add a new row
                                    $row = MOSCI.addNewInmate();
                                }

                                if (!$row || $row.length === 0) {
                                    console.error('Failed to get row for inmate #' + (i + 1));
                                    return;
                                }

                                                // Set the prefix dropdown
                                $row.find('select[id*="InmateIdPrefix"]').val(inmate.inmateIdPrefix);

                                // Set the hidden offender ID field
                                $row.find('input[id*="OffenderId"]').val(inmate.offenderId);

                                // Set the combined offender ID display field
                                $row.find('input[id*="CombinedOffenderId"]').val(inmate.inmateIdPrefix + inmate.offenderId);

                                // Set other fields
                                $row.find('input[id*="LastName"]').val(inmate.lastName);
                                $row.find('input[id*="FirstName"]').val(inmate.firstName);

                                // Set the institution dropdowns based on the institution IDs
                                console.log('Setting FromInstitutionId:', inmate.fromInstitutionId);
                                console.log('Setting ToInstitutionId:', inmate.toInstitutionId);

                                // Set From Institution dropdown
                                if (inmate.fromInstitutionId) {
                                    var $fromSelect = $row.find('select[id*="FromInstitutionId"]');
                                    console.log('From dropdown found:', $fromSelect.length);
                                    if ($fromSelect.length > 0) {
                                        $fromSelect.val(inmate.fromInstitutionId);
                                        console.log('From dropdown value set to:', $fromSelect.val());
                                        // Trigger change event to ensure proper binding
                                        $fromSelect.trigger('change');
                                    }
                                }

                                // Set To Institution dropdown with enhanced selection logic
                                if (inmate.toInstitutionId) {
                                    var $toSelect = $row.find('select[id*="ToInstitutionId"]');
                                    console.log('To dropdown found:', $toSelect.length);
                                    if ($toSelect.length > 0) {
                                        // Debug: Log available options
                                        console.log('Available options in To dropdown:');
                                        $toSelect.find('option').each(function() {
                                            console.log('  Option value:', $(this).val(), 'text:', $(this).text());
                                        });
                                        // First, try setting the value directly
                                        $toSelect.val(inmate.toInstitutionId);
                                        console.log('To dropdown value after .val():', $toSelect.val());

                                        // If direct value setting didn't work, try selecting by option value
                                        if ($toSelect.val() !== inmate.toInstitutionId.toString()) {
                                            console.log('Direct value setting failed, trying option selection');
                                            $toSelect.find('option').prop('selected', false);
                                            $toSelect.find('option[value="' + inmate.toInstitutionId + '"]').prop('selected', true);
                                            console.log('To dropdown value after option selection:', $toSelect.val());
                                        }

                                        // Trigger change event to ensure proper binding
                                        $toSelect.trigger('change');

                                        // Final verification
                                        console.log('Final To dropdown value:', $toSelect.val());
                                        console.log('Expected value:', inmate.toInstitutionId);
                                    }
                                }

                                $row.find('input[id*="SchDate"]').val(inmate.schDate);
                                $row.find('input[id*="Descrl"]').val(inmate.descrl);

                                console.log('Populated row ' + (i + 1) + ' with inmate:', inmate.inmateIdPrefix + inmate.offenderId);
                            });

                            updateButtonState();
                            alert(result.message);
                        } else {
                            alert('Error: ' + result.message);
                        }
                    },
                    error: function (xhr, status, error) {
                        $('#btnFindOffender').prop('disabled', false).html('Find Inmate');
                        console.error('AJAX Error:', status, error);
                        alert('Error finding inmate. See console for details.');
                    }
                });
            });

            // Enable Enter key to trigger search
            $("#txtInmateNum").keyup(function (event) {
                if (event.keyCode == 13) {
                    $("#btnFindOffender").click();
                }
            });

            $("#btnSearch").click(function (event) {
                event.preventDefault();
                var oaksId = $("#OaksId").val();
                $.ajax({
                    url: window.m_options ? window.m_options.GetEmployeeInfoByOaksId : '@Url.Action("GetEmployeeInfoByOaksId", "Mosci", new { area = "Transfers" })',
                    type: 'GET',
                    data: { oaksId: oaksId },
                    dataType: 'json',
                    success: function (response) {
                        if (response.Message == "No Record") {
                            alert("Please provide correct Oaks Id!");
                            return false;
                        }
                        $("#hdnOaksId").val(oaksId);
                        $("#FirstName").val(response.FirstName.toUpperCase());
                        $("#LastName").val(response.LastName.toUpperCase());
                        $("#JobTitle").val(response.JobTitle.toUpperCase());

                        // Multiselect functionality
                        $('#SelectedInsts').multiselect('deselectAll', false);
                        $('#SelectedInsts').next('.btn-group').find('.multiselect.dropdown-toggle').attr('title', 'None Selected');
                        $('#SelectedInsts').next('.btn-group').find('.multiselect-selected-text').html('None selected');
                        var selectedValues = response.SelectedInsts;
                        $('#SelectedInsts option').each(function () {
                            var optionValue = $(this).val();

                            if (selectedValues.includes(optionValue)) {
                                var res = $("#SelectedInsts").val();
                                $("#SelectedInsts").prop('selected', true);
                                $("#SelectedInsts").multiselect('select', optionValue);
                            }
                        });
                    },
                    error: function (xhr, status, error) {
                        alert("Error while getting Employee Info");
                        $("#result").html("<p>Error: " + error + "</p>");
                    }
                });
            });
        });
</script>

        <!-- Common.js is not needed since we defined MyScript above -->
        <!-- <script type="text/javascript" src="~/Areas/Rh/Scripts/Common.js"></script> -->
    }
}
